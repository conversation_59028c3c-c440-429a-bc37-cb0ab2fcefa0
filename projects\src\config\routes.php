<?php
/**
 * File cấu hình routes
 */

return [
    // Home routes
    '/' => ['controller' => 'HomeController', 'action' => 'index'],
    '/about' => ['controller' => 'HomeController', 'action' => 'about'],
    '/contact' => ['controller' => 'HomeController', 'action' => 'contact'],
    
    // Database test routes
    '/db-test' => ['controller' => 'DatabaseTestController', 'action' => 'index'],
    '/db-test/phpinfo' => ['controller' => 'DatabaseTestController', 'action' => 'phpinfo'],
    '/db-test/environment' => ['controller' => 'DatabaseTestController', 'action' => 'environment'],
    
    // User routes
    '/users' => ['controller' => 'UserController', 'action' => 'index'],
    '/users/{id}' => ['controller' => 'UserController', 'action' => 'show'],
    '/users/create' => ['controller' => 'UserController', 'action' => 'create'],
    '/users/store' => ['controller' => 'UserController', 'action' => 'store'],
    '/users/{id}/edit' => ['controller' => 'UserController', 'action' => 'edit'],
    '/users/{id}/update' => ['controller' => 'UserController', 'action' => 'update'],
    '/users/{id}/delete' => ['controller' => 'UserController', 'action' => 'delete'],
    
    // API routes
    '/api/users' => ['controller' => 'UserController', 'action' => 'apiIndex'],
    '/api/users/{id}' => ['controller' => 'UserController', 'action' => 'apiShow'],
    '/api/users/create' => ['controller' => 'UserController', 'action' => 'apiStore'],
    '/api/users/{id}/update' => ['controller' => 'UserController', 'action' => 'apiUpdate'],
    '/api/users/{id}/delete' => ['controller' => 'UserController', 'action' => 'apiDelete'],
    
    // Test routes
    '/test/redis' => ['controller' => 'DatabaseTestController', 'action' => 'testRedis'],
    '/test/mail' => ['controller' => 'DatabaseTestController', 'action' => 'testMail'],
];
