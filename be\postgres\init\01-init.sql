-- Create database for the application
CREATE DATABASE app_db;

-- Create user for the application
CREATE USER app_user WITH ENCRYPTED PASSWORD 'app_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON <PERSON>AT<PERSON>ASE app_db TO app_user;

-- Connect to the app database
\c app_db;

-- <PERSON> schema privileges
GRANT ALL ON SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO app_user;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create function to update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for users table
CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data
INSERT INTO users (name, email, password) VALUES 
    ('Nguyễn Văn A', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
    ('Trần Thị B', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
    ('Lê Văn C', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
    ('Phạm Thị D', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
    ('Hoàng Văn E', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Create posts table for demo
CREATE TABLE IF NOT EXISTS posts (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    content TEXT,
    status VARCHAR(20) DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create trigger for posts table
CREATE TRIGGER update_posts_updated_at 
    BEFORE UPDATE ON posts 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert sample posts
INSERT INTO posts (user_id, title, content, status) VALUES 
    (1, 'Bài viết đầu tiên', 'Đây là nội dung của bài viết đầu tiên.', 'published'),
    (1, 'Hướng dẫn sử dụng PHP', 'Nội dung hướng dẫn về PHP...', 'published'),
    (2, 'Giới thiệu về PostgreSQL', 'PostgreSQL là một hệ quản trị cơ sở dữ liệu...', 'published'),
    (3, 'Docker và containerization', 'Docker giúp đóng gói ứng dụng...', 'draft'),
    (4, 'Web development với PHP', 'Phát triển web hiện đại...', 'published');

-- Create categories table
CREATE TABLE IF NOT EXISTS categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample categories
INSERT INTO categories (name, description) VALUES 
    ('Technology', 'Các bài viết về công nghệ'),
    ('Programming', 'Lập trình và phát triển phần mềm'),
    ('Database', 'Cơ sở dữ liệu và quản lý dữ liệu'),
    ('Web Development', 'Phát triển ứng dụng web'),
    ('DevOps', 'Vận hành và triển khai hệ thống');

-- Create post_categories junction table
CREATE TABLE IF NOT EXISTS post_categories (
    post_id INTEGER REFERENCES posts(id) ON DELETE CASCADE,
    category_id INTEGER REFERENCES categories(id) ON DELETE CASCADE,
    PRIMARY KEY (post_id, category_id)
);

-- Insert sample post-category relationships
INSERT INTO post_categories (post_id, category_id) VALUES 
    (1, 1), (1, 4),
    (2, 2), (2, 4),
    (3, 3), (3, 1),
    (4, 1), (4, 5),
    (5, 2), (5, 4);

-- Grant permissions on new tables
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO app_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO app_user;
