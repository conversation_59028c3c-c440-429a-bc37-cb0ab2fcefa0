<?php
/**
 * PSR-4 Autoloader và helper functions
 */

// Autoloader function
spl_autoload_register(function ($className) {
    // Namespace mapping
    $namespaces = [
        'Controllers\\' => __DIR__ . '/src/controllers/',
        'Models\\' => __DIR__ . '/src/models/',
        'Utils\\' => __DIR__ . '/src/utils/',
    ];
    
    // Thử tìm class trong các namespace
    foreach ($namespaces as $namespace => $path) {
        if (strpos($className, $namespace) === 0) {
            $classFile = $path . str_replace($namespace, '', $className) . '.php';
            if (file_exists($classFile)) {
                require_once $classFile;
                return;
            }
        }
    }
    
    // Fallback: tìm trực tiếp trong thư mục controllers và models
    $paths = [
        __DIR__ . '/src/controllers/' . $className . '.php',
        __DIR__ . '/src/models/' . $className . '.php',
        __DIR__ . '/src/utils/' . $className . '.php',
    ];
    
    foreach ($paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            return;
        }
    }
});

/**
 * Load config file
 */
function loadConfig($configName) {
    $configFile = __DIR__ . '/src/config/' . $configName . '.php';
    if (file_exists($configFile)) {
        return require $configFile;
    }
    throw new Exception("Config file not found: " . $configFile);
}

/**
 * Helper function để render view
 */
function view($viewName, $data = []) {
    $viewFile = __DIR__ . '/src/views/' . $viewName . '.php';
    if (file_exists($viewFile)) {
        extract($data);
        include $viewFile;
    } else {
        throw new Exception("View file not found: " . $viewFile);
    }
}

/**
 * Helper function để tạo URL
 */
function url($path = '') {
    $baseUrl = defined('APP_URL') ? APP_URL : 'http://localhost:8080';
    return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
}

/**
 * Helper function để escape HTML
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * Helper function để debug
 */
function dd($data) {
    echo '<pre>';
    var_dump($data);
    echo '</pre>';
    die();
}

/**
 * Helper function để log
 */
function logger($message, $level = 'info') {
    $logFile = __DIR__ . '/logs/app.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[{$timestamp}] [{$level}] {$message}" . PHP_EOL;
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * Helper function để redirect
 */
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

/**
 * Helper function để lấy old input (for forms)
 */
function old($key, $default = '') {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    return $_SESSION['old_input'][$key] ?? $default;
}

/**
 * Helper function để set old input
 */
function setOldInput($data) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['old_input'] = $data;
}

/**
 * Helper function để clear old input
 */
function clearOldInput() {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    unset($_SESSION['old_input']);
}
