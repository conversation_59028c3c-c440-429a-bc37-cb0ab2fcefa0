<?php
/**
 * Autoloader cho ứng dụng
 */

spl_autoload_register(function ($className) {
    // Đ<PERSON>nh nghĩa các namespace và đường dẫn tương ứng
    $namespaces = [
        'App\\Controllers\\' => __DIR__ . '/src/controllers/',
        'App\\Models\\' => __DIR__ . '/src/models/',
        'App\\Utils\\' => __DIR__ . '/src/utils/',
        'App\\' => __DIR__ . '/src/',
    ];
    
    // Tìm namespace phù hợp
    foreach ($namespaces as $namespace => $path) {
        if (strpos($className, $namespace) === 0) {
            // Loại bỏ namespace prefix
            $relativeClass = substr($className, strlen($namespace));
            
            // Chuyển đổi namespace separators thành directory separators
            $file = $path . str_replace('\\', '/', $relativeClass) . '.php';
            
            // Nếu file tồn tại thì include
            if (file_exists($file)) {
                require_once $file;
                return;
            }
        }
    }
    
    // Fallback: tìm file theo tên class (không có namespace)
    $possiblePaths = [
        __DIR__ . '/src/controllers/' . $className . '.php',
        __DIR__ . '/src/models/' . $className . '.php',
        __DIR__ . '/src/utils/' . $className . '.php',
    ];
    
    foreach ($possiblePaths as $file) {
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Helper function để load config
function loadConfig($configName) {
    $configFile = __DIR__ . '/src/config/' . $configName . '.php';
    if (file_exists($configFile)) {
        return require $configFile;
    }
    return null;
}

// Helper function để render view
function view($viewName, $data = []) {
    extract($data);
    $viewFile = __DIR__ . '/src/views/' . $viewName . '.php';
    if (file_exists($viewFile)) {
        include $viewFile;
    } else {
        throw new Exception("View file not found: " . $viewFile);
    }
}

// Helper function để redirect
function redirect($url) {
    header('Location: ' . $url);
    exit;
}

// Helper function để lấy URL hiện tại
function getCurrentUrl() {
    return $_SERVER['REQUEST_URI'];
}

// Helper function để lấy method hiện tại
function getCurrentMethod() {
    return $_SERVER['REQUEST_METHOD'];
}
