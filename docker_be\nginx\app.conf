server {
    listen 80;
    server_name localhost;
    root /var/www; # Th<PERSON> mục gốc của dự án
    index index.php index.html;

    # Ghi lại log lỗi và truy cập
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # X<PERSON> lý các yêu cầu đến file tĩnh
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Chuyển tiếp các yêu cầu .php đến dịch vụ PHP-FPM
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;

        # Đ<PERSON> sử dụng PHP 8.4, trỏ đến dịch vụ 'php84'
        fastcgi_pass php84:9000;

        # -- GHI CHÚ --
        # <PERSON><PERSON> chuyể<PERSON> sang PHP 7.4, hãy bình luận dòng trên và bỏ bình luận dòng dưới:
        # fastcgi_pass php74:9000;

        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # Chặn truy cập vào các tệp .htaccess
    location ~ /\.ht {
        deny all;
    }
}
