[xdebug]
; Enable Xdebug
zend_extension=xdebug

; Xdebug 3 configuration
xdebug.mode = debug,develop,coverage,profile
xdebug.start_with_request = yes
xdebug.discover_client_host = true
xdebug.client_host = host.docker.internal
xdebug.client_port = 9003
xdebug.idekey = PHPSTORM

; Log settings
xdebug.log = /var/log/xdebug.log
xdebug.log_level = 0

; Profiler settings
xdebug.output_dir = /tmp/xdebug
xdebug.profiler_output_name = cachegrind.out.%t-%s

; Coverage settings
xdebug.coverage_enable = 1

; Development helpers
xdebug.var_display_max_children = 256
xdebug.var_display_max_data = 1024
xdebug.var_display_max_depth = 5

; Step debugging
xdebug.max_nesting_level = 512

; Function trace
xdebug.trace_output_dir = /tmp/xdebug
xdebug.trace_output_name = trace.%c
