<?php
/**
 * Base Model - Model cơ sở cho tất cả models
 */

class BaseModel {
    protected static $connection = null;
    protected $table = '';
    protected $primaryKey = 'id';
    protected $fillable = [];
    
    public function __construct() {
        if (self::$connection === null) {
            $this->connect();
        }
    }
    
    /**
     * Kết nối database
     */
    private function connect() {
        try {
            $config = loadConfig('database');
            $dbConfig = $config['connections'][$config['default']];
            
            $dsn = "{$dbConfig['driver']}:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']}";
            if (isset($dbConfig['charset'])) {
                $dsn .= ";charset={$dbConfig['charset']}";
            }
            
            self::$connection = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Lấy tất cả records
     */
    public function all() {
        $sql = "SELECT * FROM {$this->table}";
        $stmt = self::$connection->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Tìm record theo ID
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = self::$connection->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * Tạo record mới
     */
    public function create($data) {
        // Lọc dữ liệu theo fillable
        $filteredData = array_intersect_key($data, array_flip($this->fillable));
        
        $columns = implode(', ', array_keys($filteredData));
        $placeholders = ':' . implode(', :', array_keys($filteredData));
        
        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        $stmt = self::$connection->prepare($sql);
        
        foreach ($filteredData as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        
        $stmt->execute();
        return self::$connection->lastInsertId();
    }
    
    /**
     * Cập nhật record
     */
    public function update($id, $data) {
        // Lọc dữ liệu theo fillable
        $filteredData = array_intersect_key($data, array_flip($this->fillable));
        
        $setParts = [];
        foreach (array_keys($filteredData) as $column) {
            $setParts[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = :id";
        $stmt = self::$connection->prepare($sql);
        
        foreach ($filteredData as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Xóa record
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = self::$connection->prepare($sql);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }
    
    /**
     * Đếm số records
     */
    public function count() {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}";
        $stmt = self::$connection->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        return $result['count'];
    }
    
    /**
     * Tìm records theo điều kiện
     */
    public function where($column, $operator, $value) {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} {$operator} :value";
        $stmt = self::$connection->prepare($sql);
        $stmt->bindParam(':value', $value);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Bắt đầu transaction
     */
    public function beginTransaction() {
        return self::$connection->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return self::$connection->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return self::$connection->rollback();
    }
    
    /**
     * Execute raw SQL
     */
    public function query($sql, $params = []) {
        $stmt = self::$connection->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Get database connection
     */
    public static function getConnection() {
        return self::$connection;
    }
}
