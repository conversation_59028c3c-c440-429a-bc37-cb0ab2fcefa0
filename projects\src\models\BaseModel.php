<?php
/**
 * Base Model - Model cơ sở cho tất cả models
 */

class BaseModel {
    protected static $connection = null;
    protected $table = '';
    protected $primaryKey = 'id';
    protected $fillable = [];
    
    public function __construct() {
        if (self::$connection === null) {
            $this->connect();
        }
    }
    
    /**
     * Kết nối database
     */
    private function connect() {
        $config = loadConfig('database');
        $dbConfig = $config['connections'][$config['default']];
        
        try {
            $dsn = $this->buildDsn($dbConfig);
            self::$connection = new PDO(
                $dsn,
                $dbConfig['username'],
                $dbConfig['password'],
                $dbConfig['options']
            );
        } catch (PDOException $e) {
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }
    
    /**
     * Xây dựng DSN string
     */
    private function buildDsn($config) {
        switch ($config['driver']) {
            case 'mysql':
                return "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']};charset={$config['charset']}";
            case 'pgsql':
                return "pgsql:host={$config['host']};port={$config['port']};dbname={$config['database']}";
            default:
                throw new Exception("Unsupported database driver: " . $config['driver']);
        }
    }
    
    /**
     * Lấy tất cả records
     */
    public function all() {
        $sql = "SELECT * FROM {$this->table}";
        $stmt = self::$connection->prepare($sql);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Tìm record theo ID
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = self::$connection->prepare($sql);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        return $stmt->fetch();
    }
    
    /**
     * Tìm record đầu tiên theo điều kiện
     */
    public function where($column, $operator, $value) {
        $sql = "SELECT * FROM {$this->table} WHERE {$column} {$operator} :value";
        $stmt = self::$connection->prepare($sql);
        $stmt->bindParam(':value', $value);
        $stmt->execute();
        return $stmt->fetchAll();
    }
    
    /**
     * Tạo record mới
     */
    public function create($data) {
        // Lọc dữ liệu theo fillable
        $filteredData = $this->filterFillable($data);
        
        $columns = implode(', ', array_keys($filteredData));
        $placeholders = ':' . implode(', :', array_keys($filteredData));
        
        $sql = "INSERT INTO {$this->table} ({$columns}) VALUES ({$placeholders})";
        $stmt = self::$connection->prepare($sql);
        
        foreach ($filteredData as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        
        $stmt->execute();
        return self::$connection->lastInsertId();
    }
    
    /**
     * Cập nhật record
     */
    public function update($id, $data) {
        // Lọc dữ liệu theo fillable
        $filteredData = $this->filterFillable($data);
        
        $setParts = [];
        foreach (array_keys($filteredData) as $column) {
            $setParts[] = "{$column} = :{$column}";
        }
        $setClause = implode(', ', $setParts);
        
        $sql = "UPDATE {$this->table} SET {$setClause} WHERE {$this->primaryKey} = :id";
        $stmt = self::$connection->prepare($sql);
        
        foreach ($filteredData as $key => $value) {
            $stmt->bindValue(':' . $key, $value);
        }
        $stmt->bindValue(':id', $id);
        
        return $stmt->execute();
    }
    
    /**
     * Xóa record
     */
    public function delete($id) {
        $sql = "DELETE FROM {$this->table} WHERE {$this->primaryKey} = :id";
        $stmt = self::$connection->prepare($sql);
        $stmt->bindParam(':id', $id);
        return $stmt->execute();
    }
    
    /**
     * Lọc dữ liệu theo fillable
     */
    private function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    /**
     * Thực thi raw query
     */
    public function query($sql, $params = []) {
        $stmt = self::$connection->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetchAll();
    }
    
    /**
     * Bắt đầu transaction
     */
    public function beginTransaction() {
        return self::$connection->beginTransaction();
    }
    
    /**
     * Commit transaction
     */
    public function commit() {
        return self::$connection->commit();
    }
    
    /**
     * Rollback transaction
     */
    public function rollback() {
        return self::$connection->rollback();
    }
}
