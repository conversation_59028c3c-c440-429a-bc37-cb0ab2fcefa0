# PHP MVC Application

Ứng dụng PHP được xây dựng theo mô hình MVC với cấu trúc src chuẩn.

## Cấu trúc thư mục

```
projects/
├── src/
│   ├── controllers/     # Các controller xử lý logic
│   │   ├── BaseController.php
│   │   ├── HomeController.php
│   │   └── UserController.php
│   ├── models/         # Các model xử lý dữ liệu
│   │   ├── BaseModel.php
│   │   └── User.php
│   ├── views/          # Các template hiển thị
│   │   ├── layouts/
│   │   │   └── layout.php
│   │   └── home/
│   │       ├── index.php
│   │       ├── about.php
│   │       └── contact.php
│   ├── config/         # File cấu hình
│   │   ├── config.php
│   │   ├── database.php
│   │   └── routes.php
│   └── utils/          # Các utility class
├── public/             # File public (images, css, js)
├── assets/             # Asset files
│   ├── css/
│   ├── js/
│   └── images/
├── autoload.php        # Autoloader
├── index.php           # Entry point
├── .htaccess          # URL rewriting
└── README.md          # Tài liệu này
```

## Tính năng

- ✅ Cấu trúc MVC rõ ràng
- ✅ Autoloader tự động
- ✅ Routing đơn giản với parameter support
- ✅ Template engine cơ bản với layout
- ✅ Hỗ trợ multiple database (MySQL, PostgreSQL)
- ✅ Base Model với ORM cơ bản
- ✅ Base Controller với helper methods
- ✅ Flash message system
- ✅ Form validation
- ✅ JSON API support
- ✅ Bootstrap 5 UI

## Cài đặt

1. **Sử dụng Docker (Khuyến nghị)**
   ```bash
   cd docker_be
   docker-compose up -d
   ```

2. **Truy cập ứng dụng**
   - Website: http://localhost:8080
   - API: http://localhost:8080/api/users

## Cấu hình

### Database
Chỉnh sửa file `src/config/database.php` để cấu hình kết nối database:

```php
'mysql' => [
    'host' => 'my_mysql',
    'database' => 'your_mysql_db',
    'username' => 'your_mysql_user',
    'password' => 'your_mysql_password',
]
```

### Routes
Thêm routes mới trong file `src/config/routes.php`:

```php
'/new-route' => [
    'controller' => 'YourController',
    'action' => 'yourMethod',
    'method' => 'GET'
]
```

## Sử dụng

### Tạo Controller mới

```php
<?php
require_once 'BaseController.php';

class YourController extends BaseController {
    public function index() {
        $data = ['title' => 'Your Page'];
        $this->render('your/index', $data);
    }
}
```

### Tạo Model mới

```php
<?php
require_once 'BaseModel.php';

class YourModel extends BaseModel {
    protected $table = 'your_table';
    protected $fillable = ['field1', 'field2'];
}
```

### Tạo View mới

```php
<!-- src/views/your/index.php -->
<h1><?= htmlspecialchars($title) ?></h1>
<p>Your content here...</p>
```

## API Endpoints

- `GET /api/users` - Lấy danh sách users
- `GET /api/users/{id}` - Lấy thông tin user theo ID

## Môi trường phát triển

- PHP 8.4 hoặc 7.4
- MySQL 8.0 hoặc PostgreSQL 15
- Nginx
- Docker & Docker Compose

## Bảo mật

- XSS Protection
- CSRF Protection (cần implement)
- SQL Injection Protection (PDO prepared statements)
- Input Validation
- Password Hashing

## Đóng góp

1. Fork dự án
2. Tạo feature branch
3. Commit changes
4. Push to branch
5. Tạo Pull Request

## License

MIT License
