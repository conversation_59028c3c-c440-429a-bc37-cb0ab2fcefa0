# PHP MVC Project

Dự án PHP MVC được thiết kế để chạy với Docker Backend Environment.

## Cấu trúc Project

```
projects/
├── src/
│   ├── controllers/        # Controllers (MVC)
│   │   ├── BaseController.php
│   │   ├── HomeController.php
│   │   ├── UserController.php
│   │   └── DatabaseTestController.php
│   ├── models/            # Models (MVC)
│   │   ├── BaseModel.php
│   │   └── User.php
│   ├── views/             # Views (MVC)
│   │   ├── layouts/
│   │   │   └── layout.php
│   │   ├── home/
│   │   │   ├── index.php
│   │   │   ├── about.php
│   │   │   └── contact.php
│   │   ├── users/
│   │   │   └── index.php
│   │   └── database/
│   │       └── test.php
│   ├── config/            # Configuration files
│   │   ├── config.php
│   │   ├── database.php
│   │   └── routes.php
│   └── utils/             # Utility classes
├── autoload.php           # PSR-4 Autoloader
├── index.php              # Entry point
├── .htaccess             # URL rewriting
└── README.md             # This file
```

## Tính năng

### MVC Architecture
- **Controllers**: Xử lý logic nghiệp vụ
- **Models**: Tương tác với database
- **Views**: Hiển thị giao diện người dùng

### Database Integration
- Kết nối PostgreSQL 17 qua Docker
- BaseModel với CRUD operations
- User model với các method chuyên biệt
- Database connection pooling

### Routing System
- URL routing với parameters
- RESTful API support
- Route caching và optimization

### Template Engine
- Layout system với Bootstrap 5
- Component-based views
- Flash messaging system
- Form validation và error handling

### Development Tools
- Database testing interface
- Environment information
- PHP version switching (8.4/7.4)
- Xdebug integration

## Routes

### Web Routes
- `/` - Trang chủ
- `/about` - Giới thiệu
- `/contact` - Liên hệ (có form)
- `/users` - Danh sách users
- `/users/{id}` - Chi tiết user

### API Routes
- `/api/users` - API danh sách users
- `/api/users/{id}` - API chi tiết user
- `/api/users/create` - Tạo user mới
- `/api/users/{id}/update` - Cập nhật user
- `/api/users/{id}/delete` - Xóa user

### Development Routes
- `/db-test` - Test database connection
- `/db-test/phpinfo` - PHP information
- `/db-test/environment` - Environment details

## Database Configuration

### PostgreSQL Connection
```php
'postgres' => [
    'driver' => 'pgsql',
    'host' => 'postgres',      // Docker service name
    'port' => '5432',
    'database' => 'app_db',
    'username' => 'app_user',
    'password' => 'app_password',
]
```

### Redis Configuration
```php
'redis' => [
    'host' => 'redis',         // Docker service name
    'port' => 6379,
    'password' => 'redis_password',
]
```

## Usage Examples

### Creating a New Controller
```php
<?php
require_once 'BaseController.php';

class MyController extends BaseController {
    public function index() {
        $data = ['title' => 'My Page'];
        $this->render('my/index', $data);
    }
    
    public function apiIndex() {
        $data = ['message' => 'API response'];
        $this->json($data);
    }
}
```

### Creating a New Model
```php
<?php
require_once 'BaseModel.php';

class MyModel extends BaseModel {
    protected $table = 'my_table';
    protected $fillable = ['name', 'email'];
    
    public function findByEmail($email) {
        return $this->where('email', '=', $email);
    }
}
```

### Adding Routes
```php
// In src/config/routes.php
return [
    '/my-route' => ['controller' => 'MyController', 'action' => 'index'],
    '/my-route/{id}' => ['controller' => 'MyController', 'action' => 'show'],
];
```

## Development Workflow

1. **Start Docker Environment**
   ```bash
   cd be/
   docker-compose up -d
   ```

2. **Access Application**
   - PHP 8.4: http://localhost:8080
   - PHP 7.4: http://localhost:8074

3. **Database Management**
   - Adminer: http://localhost:8081
   - Credentials: app_user / app_password

4. **Email Testing**
   - MailHog: http://localhost:8025

5. **Development Tools**
   - Database Test: http://localhost:8080/db-test
   - PHP Info: http://localhost:8080/db-test/phpinfo

## Security Features

- CSRF protection ready
- XSS protection headers
- SQL injection prevention (PDO prepared statements)
- Input validation và sanitization
- Session security configuration
- File access restrictions (.htaccess)

## Performance Optimizations

- OPcache enabled
- Gzip compression
- Static file caching
- Database connection reuse
- Redis caching support

## Debugging

### Xdebug Configuration
- Port: 9003
- Path mapping: `/var/www` → `{project_path}/projects`

### VS Code Setup
```json
{
    "name": "Listen for Xdebug",
    "type": "php",
    "request": "launch",
    "port": 9003,
    "pathMappings": {
        "/var/www": "${workspaceFolder}/projects"
    }
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Kiểm tra Docker containers đang chạy
   - Verify database credentials
   - Check network connectivity

2. **404 Errors**
   - Kiểm tra .htaccess file
   - Verify route configuration
   - Check file permissions

3. **Xdebug Not Working**
   - Verify Xdebug extension loaded
   - Check IDE configuration
   - Confirm port 9003 available

### Logs
- Application logs: `projects/logs/app.log`
- PHP errors: Docker container logs
- Nginx logs: `be/logs/nginx/`

## Contributing

1. Follow PSR-4 autoloading standards
2. Use proper MVC separation
3. Add proper error handling
4. Write meaningful comments
5. Test with both PHP versions

## License

This project is open source and available under the MIT License.
