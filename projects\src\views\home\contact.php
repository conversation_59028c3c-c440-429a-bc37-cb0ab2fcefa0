<div class="row">
    <div class="col-12">
        <h1><?= htmlspecialchars($title) ?></h1>
        <p class="lead">Chúng tôi rất mong nhận được phản hồi từ bạn!</p>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-8">
        <!-- Contact Form -->
        <div class="card">
            <div class="card-header">
                <h5>Gửi tin nhắn cho chúng tôi</h5>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['flash']['errors'])): ?>
                    <div class="alert alert-danger">
                        <h6>Vui lòng kiểm tra lại các lỗi sau:</h6>
                        <ul class="mb-0">
                            <?php foreach ($_SESSION['flash']['errors'] as $field => $fieldErrors): ?>
                                <?php foreach ($fieldErrors as $error): ?>
                                    <li><?= htmlspecialchars($error) ?></li>
                                <?php endforeach; ?>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                    <?php unset($_SESSION['flash']['errors']); ?>
                <?php endif; ?>
                
                <form method="POST" action="/contact">
                    <div class="mb-3">
                        <label for="name" class="form-label">Họ và tên <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="name" name="name" 
                               value="<?= htmlspecialchars($_POST['name'] ?? '') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="email" name="email" 
                               value="<?= htmlspecialchars($_POST['email'] ?? '') ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">Tin nhắn <span class="text-danger">*</span></label>
                        <textarea class="form-control" id="message" name="message" rows="5" required><?= htmlspecialchars($_POST['message'] ?? '') ?></textarea>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send"></i> Gửi tin nhắn
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Contact Information -->
        <div class="card">
            <div class="card-header">
                <h5>Thông tin liên hệ</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="bi bi-geo-alt"></i> Địa chỉ</h6>
                    <p class="text-muted">123 Đường ABC, Quận XYZ<br>Thành phố Hồ Chí Minh, Việt Nam</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-telephone"></i> Điện thoại</h6>
                    <p class="text-muted">+84 123 456 789</p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-envelope"></i> Email</h6>
                    <p class="text-muted"><EMAIL></p>
                </div>
                
                <div class="mb-3">
                    <h6><i class="bi bi-clock"></i> Giờ làm việc</h6>
                    <p class="text-muted">
                        Thứ 2 - Thứ 6: 8:00 - 17:00<br>
                        Thứ 7: 8:00 - 12:00<br>
                        Chủ nhật: Nghỉ
                    </p>
                </div>
            </div>
        </div>
        
        <!-- FAQ -->
        <div class="card mt-3">
            <div class="card-header">
                <h5>Câu hỏi thường gặp</h5>
            </div>
            <div class="card-body">
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq1">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                                Làm thế nào để sử dụng API?
                            </button>
                        </h2>
                        <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Bạn có thể truy cập API tại <code>/api/users</code> để xem danh sách users hoặc <code>/api/users/{id}</code> để xem chi tiết.
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="faq2">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                                Ứng dụng hỗ trợ những database nào?
                            </button>
                        </h2>
                        <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Hiện tại hỗ trợ MySQL và PostgreSQL thông qua PDO.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
