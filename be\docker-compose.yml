version: '3.8'

services:
  # Nginx Web Server
  nginx:
    image: nginx:1.25-alpine
    container_name: app_nginx
    restart: unless-stopped
    ports:
      - "8080:80"      # PHP 8.4 (default)
      - "8074:8074"    # PHP 7.4
    volumes:
      - ../projects:/var/www
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - php84
      - php74
    networks:
      - app-network

  # PHP 8.4 with Xdebug
  php84:
    build:
      context: ./php8.4
      dockerfile: Dockerfile
    container_name: app_php84
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ../projects:/var/www
      - ./logs/php84:/var/log
    environment:
      - PHP_IDE_CONFIG=serverName=localhost
      - XDEBUG_CONFIG=client_host=host.docker.internal
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - app-network

  # PHP 7.4 with Xdebug
  php74:
    build:
      context: ./php7.4
      dockerfile: Dockerfile
    container_name: app_php74
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ../projects:/var/www
      - ./logs/php74:/var/log
    environment:
      - PHP_IDE_CONFIG=serverName=localhost74
      - XDEBUG_CONFIG=client_host=host.docker.internal
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - app-network

  # PostgreSQL 17
  postgres:
    image: postgres:17-alpine
    container_name: app_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_password
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./postgres/init:/docker-entrypoint-initdb.d
      - ./postgres/postgresql.conf:/etc/postgresql/postgresql.conf
      - ./logs/postgres:/var/log/postgresql
    command: postgres -c config_file=/etc/postgresql/postgresql.conf
    networks:
      - app-network

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: app_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    command: redis-server --appendonly yes --requirepass redis_password
    networks:
      - app-network

  # Adminer for database management
  adminer:
    image: adminer:4.8.1
    container_name: app_adminer
    restart: unless-stopped
    ports:
      - "8081:8080"
    environment:
      ADMINER_DEFAULT_SERVER: postgres
      ADMINER_DESIGN: pepa-linha
    depends_on:
      - postgres
    networks:
      - app-network

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:v1.0.1
    container_name: app_mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - app-network

  # Node.js for frontend build tools (optional)
  node:
    image: node:18-alpine
    container_name: app_node
    working_dir: /var/www
    volumes:
      - ../projects:/var/www
    command: tail -f /dev/null
    networks:
      - app-network

# Networks
networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# Volumes
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
