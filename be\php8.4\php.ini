[PHP]
; Maximum execution time of each script, in seconds
max_execution_time = 300

; Maximum amount of time each script may spend parsing request data
max_input_time = 300

; Maximum input variable nesting level
max_input_nesting_level = 64

; Maximum amount of memory a script may consume
memory_limit = 512M

; Maximum allowed size for uploaded files
upload_max_filesize = 100M

; Maximum number of files that can be uploaded via a single request
max_file_uploads = 20

; Maximum size of POST data that PHP will accept
post_max_size = 100M

; Whether to allow HTTP file uploads
file_uploads = On

; Default timeout for socket based streams (seconds)
default_socket_timeout = 60

; Error reporting level
error_reporting = E_ALL

; Display errors to stdout
display_errors = On

; Display startup errors
display_startup_errors = On

; Log errors to specified file
log_errors = On

; Set maximum length of log_errors
log_errors_max_len = 1024

; Do not log repeated messages
ignore_repeated_errors = Off

; Ignore repeated source
ignore_repeated_source = Off

; Report memleaks
report_memleaks = On

; Store the last error/warning message in $php_errormsg
track_errors = Off

; Turn off normal error reporting and emit XML-RPC error XML
xmlrpc_errors = Off

; An array of hostname:port entries for memcache servers
session.save_handler = files

; Argument passed to save_handler
session.save_path = "/tmp"

; Whether to use cookies
session.use_cookies = 1

; Name of the session
session.name = PHPSESSID

; Initialize session on request startup
session.auto_start = 0

; Lifetime in seconds of cookie
session.cookie_lifetime = 0

; The path for which the cookie is valid
session.cookie_path = /

; The domain for which the cookie is valid
session.cookie_domain =

; Whether or not to add the httpOnly flag to the cookie
session.cookie_httponly = 1

; Add SameSite attribute to cookie
session.cookie_samesite = "Lax"

; Handler used to serialize data
session.serialize_handler = php

; Percentual probability that the 'garbage collection' process is started on every session initialization
session.gc_probability = 1
session.gc_divisor = 1000

; After this number of seconds, stored data will be seen as 'garbage' and cleaned up
session.gc_maxlifetime = 1440

; Check HTTP Referer to invalidate externally stored URLs containing ids
session.referer_check =

; How many bytes to read from the file
session.entropy_length = 32

; Specified here to create the session id
session.entropy_file = /dev/urandom

; Set to {nocache,private,public,} to determine HTTP caching aspects
session.cache_limiter = nocache

; Document expires after n minutes
session.cache_expire = 180

; trans sid support is enabled by default.
session.use_trans_sid = 0

; Select a hash function for use in generating session ids
session.hash_function = sha256

; Define how many bits are stored in each character when converting the binary hash data to something readable
session.hash_bits_per_character = 5

; The URL rewriter will look for URLs in a defined set of HTML tags
url_rewriter.tags = "a=href,area=href,frame=src,input=src,form=fakeentry"

; Enable/disable transparent session id propagation
session.trans_sid_tags = "a=href,area=href,frame=src,form="

; Timezone
date.timezone = "Asia/Ho_Chi_Minh"

; OPcache settings
opcache.enable = 1
opcache.enable_cli = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 2
opcache.fast_shutdown = 1
