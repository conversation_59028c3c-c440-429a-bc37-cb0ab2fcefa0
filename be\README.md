# Docker Backend Environment

Môi trường Docker hoàn chỉnh cho phát triển PHP với Nginx, PHP 8.4/7.4, PostgreSQL 17, Redis và Xdebug.

## Services

### Web Server
- **Nginx 1.25**: Reverse proxy và web server
  - Port 8080: PHP 8.4 (mặc định)
  - Port 8074: PHP 7.4

### PHP
- **PHP 8.4-FPM**: Với Xdebug 3.3.1
- **PHP 7.4-FPM**: Với Xdebug 3.1.6
- Extensions: PDO, PostgreSQL, MySQL, GD, ZIP, Intl, OPcache, v.v.

### Database
- **PostgreSQL 17**: Database chính
  - Port 5432
  - Database: `app_db`
  - User: `app_user`
  - Password: `app_password`

### Cache & Session
- **Redis 7**: Caching và session storage
  - Port 6379
  - Password: `redis_password`

### Development Tools
- **Adminer**: Database management UI (Port 8081)
- **MailHog**: Email testing (SMTP: 1025, Web: 8025)
- **Node.js 18**: Frontend build tools

## Cài đặt và Chạy

### 1. <PERSON><PERSON> và Setup
```bash
cd be
cp .env.example .env
```

### 2. Build và Start Services
```bash
docker-compose up -d --build
```

### 3. Kiểm tra Services
```bash
docker-compose ps
```

### 4. Xem Logs
```bash
# Tất cả services
docker-compose logs -f

# Service cụ thể
docker-compose logs -f nginx
docker-compose logs -f php84
docker-compose logs -f postgres
```

## Truy cập Services

- **Website (PHP 8.4)**: http://localhost:8080
- **Website (PHP 7.4)**: http://localhost:8074
- **Adminer**: http://localhost:8081
- **MailHog**: http://localhost:8025
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379

## Xdebug Configuration

### VS Code
Thêm vào `.vscode/launch.json`:
```json
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Listen for Xdebug (PHP 8.4)",
            "type": "php",
            "request": "launch",
            "port": 9003,
            "pathMappings": {
                "/var/www": "${workspaceFolder}/projects"
            }
        }
    ]
}
```

### PhpStorm
1. Settings → PHP → Servers
2. Tạo server mới:
   - Name: `localhost`
   - Host: `localhost`
   - Port: `8080`
   - Debugger: `Xdebug`
   - Path mappings: `/var/www` → `{project_path}/projects`

## Database Connection

### Từ Application
```php
$host = 'postgres';
$port = '5432';
$dbname = 'app_db';
$username = 'app_user';
$password = 'app_password';
```

### Từ Host Machine
```php
$host = 'localhost';
$port = '5432';
$dbname = 'app_db';
$username = 'app_user';
$password = 'app_password';
```

## Useful Commands

### Container Management
```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Rebuild services
docker-compose up -d --build

# Restart specific service
docker-compose restart nginx
```

### Database Operations
```bash
# Connect to PostgreSQL
docker-compose exec postgres psql -U postgres -d app_db

# Backup database
docker-compose exec postgres pg_dump -U postgres app_db > backup.sql

# Restore database
docker-compose exec -T postgres psql -U postgres app_db < backup.sql
```

### PHP Commands
```bash
# Execute PHP commands in PHP 8.4 container
docker-compose exec php84 php -v
docker-compose exec php84 composer install

# Execute PHP commands in PHP 7.4 container
docker-compose exec php74 php -v
docker-compose exec php74 composer install
```

### Logs
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f nginx
docker-compose logs -f php84
docker-compose logs -f postgres

# View last 100 lines
docker-compose logs --tail=100 php84
```

## Troubleshooting

### Permission Issues
```bash
# Fix file permissions
sudo chown -R $USER:$USER ../projects
chmod -R 755 ../projects
```

### Xdebug Not Working
1. Kiểm tra Xdebug extension:
   ```bash
   docker-compose exec php84 php -m | grep xdebug
   ```

2. Kiểm tra cấu hình:
   ```bash
   docker-compose exec php84 php --ini
   ```

3. Kiểm tra logs:
   ```bash
   docker-compose logs php84
   ```

### Database Connection Issues
1. Kiểm tra PostgreSQL status:
   ```bash
   docker-compose exec postgres pg_isready -U postgres
   ```

2. Kiểm tra database tồn tại:
   ```bash
   docker-compose exec postgres psql -U postgres -l
   ```

## Performance Tuning

### PHP OPcache
OPcache đã được enable mặc định. Để kiểm tra:
```bash
docker-compose exec php84 php -i | grep opcache
```

### PostgreSQL
Cấu hình PostgreSQL đã được tối ưu trong `postgres/postgresql.conf`.

### Nginx
Gzip compression và caching đã được cấu hình trong `nginx/default.conf`.

## Security Notes

- Đổi passwords mặc định trong production
- Sử dụng SSL/TLS cho production
- Cấu hình firewall phù hợp
- Regular backup database
- Update images thường xuyên
