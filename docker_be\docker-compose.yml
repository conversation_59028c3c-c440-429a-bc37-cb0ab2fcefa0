version: '3.8'

services:
  # Dị<PERSON> vụ Nginx Web Server
  nginx:
    image: nginx:stable-alpine
    container_name: my_nginx
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./your-project-folder:/var/www
      - ./nginx/conf.d:/etc/nginx/conf.d/
    depends_on:
      - php84
      - php74
    networks:
      - app-network

  # Dịch vụ PHP 8.4
  php84:
    build:
      context: ./php84
    container_name: my_php84
    restart: unless-stopped
    working_dir: /var/www/
    volumes:
      - ./your-project-folder:/var/www
    networks:
      - app-network

  # Dịch vụ PHP 7.4
  php74:
    build:
      context: ./php74
    container_name: my_php74
    restart: unless-stopped
    working_dir: /var/www/
    volumes:
      - ./your-project-folder:/var/www
    networks:
      - app-network

  # Dịch vụ PostgreSQL
  postgres:
    image: postgres:15-alpine
    container_name: my_postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: your_db_name
      POSTGRES_USER: your_user
      POSTGRES_PASSWORD: your_password
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - app-network

  # Dịch vụ MySQL
  mysql:
    image: mysql:8.0
    container_name: my_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: your_mysql_db
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_USER: your_mysql_user
      MYSQL_PASSWORD: your_mysql_password
    ports:
      - "3306:3306"
    volumes:
      - ./mysql/data:/var/lib/mysql
    networks:
      - app-network

# Định nghĩa network và volume
networks:
  app-network:
    driver: bridge

volumes:
  pgdata:
    driver: local
