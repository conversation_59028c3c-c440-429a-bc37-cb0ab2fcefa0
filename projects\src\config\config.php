<?php
/**
 * File cấu hình chính của ứng dụng
 */

// Môi trường ứng dụng
define('APP_ENV', 'development');
define('APP_DEBUG', true);
define('APP_NAME', 'My PHP App');
define('APP_URL', 'http://localhost:8080');

// Đường dẫn
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('SRC_PATH', ROOT_PATH . '/src');
define('VIEWS_PATH', SRC_PATH . '/views');
define('CONFIG_PATH', SRC_PATH . '/config');

// Timezone
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Session configuration
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS

// Error reporting
if (APP_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');

return [
    'app' => [
        'name' => APP_NAME,
        'env' => APP_ENV,
        'debug' => APP_DEBUG,
        'url' => APP_URL,
        'timezone' => 'Asia/Ho_Chi_Minh'
    ],
    
    'paths' => [
        'root' => ROOT_PATH,
        'src' => SRC_PATH,
        'views' => VIEWS_PATH,
        'config' => CONFIG_PATH
    ],
    
    'security' => [
        'csrf_token' => bin2hex(random_bytes(32)),
        'session_lifetime' => 7200, // 2 hours
        'password_min_length' => 8
    ]
];
