<?php
/**
 * Base Controller - Controller cơ sở cho tất cả controllers
 */

class BaseController {
    protected $data = [];
    
    public function __construct() {
        // Khởi tạo dữ liệu chung
        $this->data['app_name'] = APP_NAME;
        $this->data['app_url'] = APP_URL;
    }
    
    /**
     * Render view với layout
     */
    protected function render($viewName, $data = [], $layout = 'layout') {
        // Merge dữ liệu
        $this->data = array_merge($this->data, $data);
        
        // Bắt đầu output buffering để capture nội dung view
        ob_start();
        
        // Include view file
        $viewFile = VIEWS_PATH . '/' . $viewName . '.php';
        if (file_exists($viewFile)) {
            extract($this->data);
            include $viewFile;
        } else {
            throw new Exception("View file not found: " . $viewFile);
        }
        
        // Lấy nội dung view
        $content = ob_get_clean();
        
        // Render với layout
        if ($layout) {
            $layoutFile = VIEWS_PATH . '/layouts/' . $layout . '.php';
            if (file_exists($layoutFile)) {
                $this->data['content'] = $content;
                extract($this->data);
                include $layoutFile;
            } else {
                // Nếu không có layout, hiển thị trực tiếp content
                echo $content;
            }
        } else {
            echo $content;
        }
    }
    
    /**
     * Render JSON response
     */
    protected function json($data, $statusCode = 200) {
        http_response_code($statusCode);
        header('Content-Type: application/json');
        echo json_encode($data, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Redirect đến URL khác
     */
    protected function redirect($url) {
        header('Location: ' . $url);
        exit;
    }
    
    /**
     * Lấy input từ request
     */
    protected function input($key = null, $default = null) {
        if ($key === null) {
            return $_REQUEST;
        }
        
        return $_REQUEST[$key] ?? $default;
    }
    
    /**
     * Validate input
     */
    protected function validate($rules) {
        $errors = [];
        
        foreach ($rules as $field => $rule) {
            $value = $this->input($field);
            $ruleArray = explode('|', $rule);
            
            foreach ($ruleArray as $singleRule) {
                if ($singleRule === 'required' && empty($value)) {
                    $errors[$field][] = "Trường {$field} là bắt buộc";
                }
                
                if (strpos($singleRule, 'min:') === 0 && !empty($value)) {
                    $min = (int)substr($singleRule, 4);
                    if (strlen($value) < $min) {
                        $errors[$field][] = "Trường {$field} phải có ít nhất {$min} ký tự";
                    }
                }
                
                if (strpos($singleRule, 'max:') === 0 && !empty($value)) {
                    $max = (int)substr($singleRule, 4);
                    if (strlen($value) > $max) {
                        $errors[$field][] = "Trường {$field} không được vượt quá {$max} ký tự";
                    }
                }
                
                if ($singleRule === 'email' && !empty($value)) {
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[$field][] = "Trường {$field} phải là email hợp lệ";
                    }
                }
            }
        }
        
        return $errors;
    }
    
    /**
     * Set flash message
     */
    protected function setFlash($type, $message) {
        $_SESSION['flash'][$type] = $message;
    }
    
    /**
     * Get flash message
     */
    protected function getFlash($type) {
        if (isset($_SESSION['flash'][$type])) {
            $message = $_SESSION['flash'][$type];
            unset($_SESSION['flash'][$type]);
            return $message;
        }
        return null;
    }
}
