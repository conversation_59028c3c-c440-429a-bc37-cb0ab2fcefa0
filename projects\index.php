<?php
/**
 * Entry point của ứng dụng
 */

// Bắt đầu session
session_start();

// Load autoloader
require_once __DIR__ . '/autoload.php';

// Load config
require_once __DIR__ . '/src/config/config.php';

// Simple Router
class Router {
    private $routes;

    public function __construct() {
        $this->routes = loadConfig('routes');
    }

    public function dispatch() {
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $method = $_SERVER['REQUEST_METHOD'];

        // Tìm route phù hợp
        foreach ($this->routes as $route => $config) {
            if ($this->matchRoute($route, $uri) && $config['method'] === $method) {
                $this->executeRoute($config, $uri, $route);
                return;
            }
        }

        // Nếu không tìm thấy route, hiển thị 404
        $this->show404();
    }

    private function matchRoute($route, $uri) {
        // Chuyển đổi route pattern thành regex
        $pattern = preg_replace('/\{[^}]+\}/', '([^/]+)', $route);
        $pattern = '#^' . $pattern . '$#';

        return preg_match($pattern, $uri);
    }

    private function executeRoute($config, $uri, $route) {
        $controllerName = $config['controller'];
        $action = $config['action'];

        // Tạo instance của controller
        if (class_exists($controllerName)) {
            $controller = new $controllerName();

            // Gọi action
            if (method_exists($controller, $action)) {
                // Lấy parameters từ URL nếu có
                $params = $this->extractParams($route, $uri);
                call_user_func_array([$controller, $action], $params);
            } else {
                $this->show404();
            }
        } else {
            $this->show404();
        }
    }

    private function extractParams($route, $uri) {
        $routeParts = explode('/', trim($route, '/'));
        $uriParts = explode('/', trim($uri, '/'));
        $params = [];

        for ($i = 0; $i < count($routeParts); $i++) {
            if (isset($routeParts[$i]) && preg_match('/\{([^}]+)\}/', $routeParts[$i], $matches)) {
                $params[] = $uriParts[$i] ?? null;
            }
        }

        return $params;
    }

    private function show404() {
        http_response_code(404);
        echo "<h1>404 - Page Not Found</h1>";
        echo "<p>The requested page could not be found.</p>";
    }
}

// Khởi tạo và chạy router
try {
    $router = new Router();
    $router->dispatch();
} catch (Exception $e) {
    if (APP_DEBUG) {
        echo "<h1>Error</h1>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    } else {
        echo "<h1>Something went wrong</h1>";
        echo "<p>Please try again later.</p>";
    }
}