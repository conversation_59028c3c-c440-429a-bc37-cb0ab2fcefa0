<?php
/**
 * Entry point của ứng dụng
 */

// Start session
session_start();

// Load autoloader và config
require_once 'autoload.php';
require_once 'src/config/config.php';

/**
 * Simple Router Class
 */
class Router {
    private $routes = [];
    
    public function __construct() {
        $this->routes = loadConfig('routes');
    }
    
    public function dispatch() {
        $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $uri = rtrim($uri, '/');
        if (empty($uri)) {
            $uri = '/';
        }
        
        // Tìm route exact match trước
        if (isset($this->routes[$uri])) {
            $this->executeRoute($this->routes[$uri]);
            return;
        }
        
        // Tìm route với parameters
        foreach ($this->routes as $route => $config) {
            if (strpos($route, '{') !== false) {
                $pattern = $this->convertRouteToRegex($route);
                if (preg_match($pattern, $uri, $matches)) {
                    // Extract parameters
                    $params = $this->extractParams($route, $matches);
                    $this->executeRoute($config, $params);
                    return;
                }
            }
        }
        
        // 404 Not Found
        $this->show404();
    }
    
    private function convertRouteToRegex($route) {
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $route);
        return '#^' . $pattern . '$#';
    }
    
    private function extractParams($route, $matches) {
        $params = [];
        preg_match_all('/\{([^}]+)\}/', $route, $paramNames);
        
        for ($i = 1; $i < count($matches); $i++) {
            $paramName = $paramNames[1][$i - 1];
            $params[$paramName] = $matches[$i];
        }
        
        return $params;
    }
    
    private function executeRoute($config, $params = []) {
        try {
            $controllerName = $config['controller'];
            $actionName = $config['action'];
            
            // Load controller
            $controllerFile = SRC_PATH . '/controllers/' . $controllerName . '.php';
            if (!file_exists($controllerFile)) {
                throw new Exception("Controller file not found: " . $controllerFile);
            }
            
            require_once $controllerFile;
            
            if (!class_exists($controllerName)) {
                throw new Exception("Controller class not found: " . $controllerName);
            }
            
            $controller = new $controllerName();
            
            if (!method_exists($controller, $actionName)) {
                throw new Exception("Action method not found: " . $actionName);
            }
            
            // Execute action with parameters
            if (!empty($params)) {
                call_user_func_array([$controller, $actionName], $params);
            } else {
                $controller->$actionName();
            }
            
        } catch (Exception $e) {
            $this->showError($e->getMessage());
        }
    }
    
    private function show404() {
        http_response_code(404);
        echo '<h1>404 - Page Not Found</h1>';
        echo '<p>The requested page could not be found.</p>';
        echo '<a href="/">Go to Homepage</a>';
    }
    
    private function showError($message) {
        http_response_code(500);
        if (APP_DEBUG) {
            echo '<h1>Error</h1>';
            echo '<p>' . htmlspecialchars($message) . '</p>';
            echo '<pre>' . htmlspecialchars(debug_backtrace()[0]['file'] ?? '') . '</pre>';
        } else {
            echo '<h1>Internal Server Error</h1>';
            echo '<p>Something went wrong. Please try again later.</p>';
        }
    }
}

// Error handling
set_error_handler(function($severity, $message, $file, $line) {
    if (APP_DEBUG) {
        echo "<b>Error:</b> $message in <b>$file</b> on line <b>$line</b><br>";
    }
    logger("Error: $message in $file on line $line", 'error');
});

set_exception_handler(function($exception) {
    if (APP_DEBUG) {
        echo "<b>Uncaught Exception:</b> " . $exception->getMessage() . "<br>";
        echo "<b>File:</b> " . $exception->getFile() . "<br>";
        echo "<b>Line:</b> " . $exception->getLine() . "<br>";
        echo "<pre>" . $exception->getTraceAsString() . "</pre>";
    } else {
        echo '<h1>Internal Server Error</h1>';
    }
    logger("Uncaught Exception: " . $exception->getMessage(), 'error');
});

// Initialize and run router
try {
    $router = new Router();
    $router->dispatch();
} catch (Exception $e) {
    if (APP_DEBUG) {
        echo '<h1>Router Error</h1>';
        echo '<p>' . htmlspecialchars($e->getMessage()) . '</p>';
    } else {
        echo '<h1>Internal Server Error</h1>';
    }
    logger("Router Error: " . $e->getMessage(), 'error');
}
