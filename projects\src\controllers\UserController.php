<?php
/**
 * User Controller - Xử lý API cho User
 */

require_once 'BaseController.php';

class UserController extends BaseController {
    
    public function index() {
        // Giả lập dữ liệu users
        $users = [
            ['id' => 1, 'name' => 'Nguyễn Văn A', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Trần Thị B', 'email' => '<EMAIL>'],
            ['id' => 3, 'name' => 'Lê Văn C', 'email' => '<EMAIL>']
        ];
        
        $this->json([
            'success' => true,
            'data' => $users,
            'message' => 'Lấy danh sách users thành công'
        ]);
    }
    
    public function show($id) {
        // Giả lập tìm user theo ID
        $users = [
            1 => ['id' => 1, 'name' => 'Nguyễn Văn A', 'email' => '<EMAIL>', 'created_at' => '2024-01-01'],
            2 => ['id' => 2, 'name' => 'Trần Thị B', 'email' => '<EMAIL>', 'created_at' => '2024-01-02'],
            3 => ['id' => 3, 'name' => 'Lê Văn C', 'email' => '<EMAIL>', 'created_at' => '2024-01-03']
        ];
        
        if (!isset($users[$id])) {
            $this->json([
                'success' => false,
                'message' => 'Không tìm thấy user'
            ], 404);
            return;
        }
        
        $this->json([
            'success' => true,
            'data' => $users[$id],
            'message' => 'Lấy thông tin user thành công'
        ]);
    }
}
