<div class="row">
    <div class="col-12">
        <!-- Hero Section -->
        <div class="jumbotron bg-light p-5 rounded-3 mb-4">
            <div class="container-fluid py-5">
                <h1 class="display-5 fw-bold"><?= htmlspecialchars($message) ?></h1>
                <p class="col-md-8 fs-4">
                    Đ<PERSON><PERSON> là ứng dụng PHP được xây dựng theo mô hình MVC với cấu trúc src chuẩn.
                    Ứng dụng hỗ trợ routing, autoloader, và template engine cơ bản.
                </p>
                <a class="btn btn-primary btn-lg" href="/about" role="button">Tìm hiểu thêm</a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-gear"></i> <PERSON><PERSON><PERSON> trúc <PERSON>
                </h5>
                <p class="card-text">
                    Ứng dụng được tổ chức theo mô hình Model-View-Controller, 
                    giúp code dễ bảo trì và mở rộng.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-database"></i> Hỗ trợ Database
                </h5>
                <p class="card-text">
                    Hỗ trợ kết nối với MySQL và PostgreSQL thông qua PDO,
                    với các phương thức ORM cơ bản.
                </p>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="bi bi-code-slash"></i> API Ready
                </h5>
                <p class="card-text">
                    Sẵn sàng để xây dựng API RESTful với JSON response
                    và routing linh hoạt.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">Thông tin hệ thống</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Thời gian hiện tại:</strong> <?= $current_time ?></p>
                        <p><strong>PHP Version:</strong> <?= PHP_VERSION ?></p>
                        <p><strong>Server:</strong> <?= $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' ?></p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Document Root:</strong> <?= $_SERVER['DOCUMENT_ROOT'] ?></p>
                        <p><strong>Request URI:</strong> <?= $_SERVER['REQUEST_URI'] ?></p>
                        <p><strong>Request Method:</strong> <?= $_SERVER['REQUEST_METHOD'] ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="bi bi-info-circle"></i> Hướng dẫn sử dụng:</h6>
            <ul class="mb-0">
                <li>Truy cập <code>/about</code> để xem thông tin về ứng dụng</li>
                <li>Truy cập <code>/contact</code> để xem form liên hệ</li>
                <li>Truy cập <code>/api/users</code> để xem API demo</li>
                <li>Truy cập <code>/api/users/1</code> để xem chi tiết user</li>
            </ul>
        </div>
    </div>
</div>
