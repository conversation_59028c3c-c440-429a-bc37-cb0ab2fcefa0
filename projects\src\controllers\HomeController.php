<?php
/**
 * Home Controller - <PERSON><PERSON> lý các trang chính của website
 */

require_once 'BaseController.php';

class HomeController extends BaseController {
    
    public function index() {
        $data = [
            'title' => 'Trang chủ',
            'message' => 'Chào mừng bạn đến với ứng dụng PHP!',
            'current_time' => date('Y-m-d H:i:s')
        ];
        
        $this->render('home/index', $data);
    }
    
    public function about() {
        $data = [
            'title' => 'Về chúng tôi',
            'description' => 'Đây là trang giới thiệu về ứng dụng của chúng tôi.',
            'features' => [
                'Cấu trúc MVC rõ ràng',
                'Autoloader tự động',
                'Routing đơn giản',
                'Template engine cơ bản',
                'Hỗ trợ multiple database'
            ]
        ];
        
        $this->render('home/about', $data);
    }
    
    public function contact() {
        $data = [
            'title' => '<PERSON><PERSON><PERSON> hệ',
            'success' => $this->getFlash('success'),
            'error' => $this->getFlash('error')
        ];
        
        $this->render('home/contact', $data);
    }
    
    public function submitContact() {
        // Validate input
        $errors = $this->validate([
            'name' => 'required|min:2|max:50',
            'email' => 'required|email',
            'message' => 'required|min:10|max:500'
        ]);
        
        if (!empty($errors)) {
            $this->setFlash('error', 'Vui lòng kiểm tra lại thông tin đã nhập');
            $this->setFlash('errors', $errors);
            $this->redirect('/contact');
            return;
        }
        
        // Lấy dữ liệu từ form
        $name = $this->input('name');
        $email = $this->input('email');
        $message = $this->input('message');
        
        // Xử lý logic gửi email hoặc lưu database ở đây
        // ...
        
        // Thông báo thành công
        $this->setFlash('success', 'Cảm ơn bạn đã liên hệ! Chúng tôi sẽ phản hồi sớm nhất có thể.');
        $this->redirect('/contact');
    }
}
